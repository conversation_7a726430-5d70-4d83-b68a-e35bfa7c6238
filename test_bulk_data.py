#!/usr/bin/env python3
"""
Test script for bulk data functionality.
Tests the fixed authentication, datetime formatting, and bulk execution.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from core.logging import get_logger
from database.connection import check_database_connection
from services.bulk_data_service import BulkDataService
from database.models import MarketType

logger = get_logger(__name__)

def test_authentication():
    """Test Fyers authentication."""
    logger.info("🔐 Testing Fyers authentication...")
    
    try:
        bulk_service = BulkDataService()
        logger.info("✅ Fyers authentication successful")
        return True
    except Exception as e:
        logger.error(f"❌ Fyers authentication failed: {e}")
        return False

def test_single_symbol():
    """Test fetching data for a single symbol."""
    logger.info("📊 Testing single symbol data fetch...")
    
    try:
        bulk_service = BulkDataService()
        
        # Test with a small date range
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=2)  # Just 2 days for testing
        
        results = bulk_service.populate_historical_data(
            symbols=["RELIANCE"],
            market_type=MarketType.EQUITY,
            start_date=start_date,
            end_date=end_date
        )
        
        if results.get("RELIANCE", False):
            logger.info("✅ Single symbol test successful")
            return True
        else:
            logger.error("❌ Single symbol test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Single symbol test error: {e}")
        return False

def test_data_summary():
    """Test data summary functionality."""
    logger.info("📈 Testing data summary...")
    
    try:
        bulk_service = BulkDataService()
        
        summary = bulk_service.get_data_summary(MarketType.EQUITY, ["RELIANCE"])
        
        if 'error' not in summary:
            logger.info(f"✅ Data summary successful: {summary}")
            return True
        else:
            logger.error(f"❌ Data summary failed: {summary}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data summary error: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting bulk data functionality tests...")
    logger.info("=" * 60)
    
    # Check database connection first
    if not check_database_connection():
        logger.error("❌ Database connection failed")
        return False
    
    logger.info("✅ Database connection successful")
    
    # Run tests
    tests = [
        ("Authentication", test_authentication),
        ("Single Symbol", test_single_symbol),
        ("Data Summary", test_data_summary)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"Test '{test_name}' failed")
        except Exception as e:
            logger.error(f"Test '{test_name}' crashed: {e}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 TEST SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Bulk data functionality is working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
